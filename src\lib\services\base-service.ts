/**
 * Base Service Class
 * 
 * This file provides the base service implementation that all domain services extend.
 * It implements common patterns for business logic operations and error handling.
 * 
 * Services contain business logic and orchestrate operations across multiple repositories.
 * They provide a clean API for the presentation layer while keeping business rules centralized.
 */

import { RepositoryFactory } from '../repositories';

// ============================================================================
// Service Response Types
// ============================================================================

export interface ServiceResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ServiceError;
  message?: string;
}

export interface ServiceError {
  code: string;
  message: string;
  details?: any;
  field?: string; // For validation errors
}

export interface PaginationOptions {
  page?: number;
  limit?: number;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// ============================================================================
// Base Service Implementation
// ============================================================================

export abstract class BaseService {
  protected repositories: RepositoryFactory;

  constructor(repositories: RepositoryFactory) {
    this.repositories = repositories;
  }

  // ============================================================================
  // Response Helpers
  // ============================================================================

  protected success<T>(data: T, message?: string): ServiceResponse<T> {
    return {
      success: true,
      data,
      message
    };
  }

  protected error(
    code: string,
    message: string,
    details?: any,
    field?: string
  ): ServiceResponse {
    return {
      success: false,
      error: {
        code,
        message,
        details,
        field
      }
    };
  }

  protected validationError(field: string, message: string): ServiceResponse {
    return this.error('VALIDATION_ERROR', message, null, field);
  }

  protected notFoundError(resource: string, id?: string): ServiceResponse {
    const message = id 
      ? `${resource} with id ${id} not found`
      : `${resource} not found`;
    
    return this.error('NOT_FOUND', message);
  }

  protected unauthorizedError(message: string = 'Unauthorized access'): ServiceResponse {
    return this.error('UNAUTHORIZED', message);
  }

  protected forbiddenError(message: string = 'Access forbidden'): ServiceResponse {
    return this.error('FORBIDDEN', message);
  }

  protected conflictError(message: string): ServiceResponse {
    return this.error('CONFLICT', message);
  }

  protected internalError(message: string, details?: any): ServiceResponse {
    return this.error('INTERNAL_ERROR', message, details);
  }

  // ============================================================================
  // Pagination Helpers
  // ============================================================================

  protected calculatePagination(
    total: number,
    page: number = 1,
    limit: number = 10
  ): PaginatedResponse<any>['pagination'] {
    const totalPages = Math.ceil(total / limit);
    
    return {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    };
  }

  protected validatePaginationOptions(options: PaginationOptions): {
    page: number;
    limit: number;
    offset: number;
  } {
    const page = Math.max(1, options.page || 1);
    const limit = Math.min(100, Math.max(1, options.limit || 10)); // Max 100 items per page
    const offset = (page - 1) * limit;

    return { page, limit, offset };
  }

  // ============================================================================
  // Validation Helpers
  // ============================================================================

  protected validateRequired(value: any, fieldName: string): ServiceResponse | null {
    if (value === null || value === undefined || value === '') {
      return this.validationError(fieldName, `${fieldName} is required`);
    }
    return null;
  }

  protected validateEmail(email: string): ServiceResponse | null {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return this.validationError('email', 'Invalid email format');
    }
    return null;
  }

  protected validateLength(
    value: string,
    fieldName: string,
    min?: number,
    max?: number
  ): ServiceResponse | null {
    if (min && value.length < min) {
      return this.validationError(
        fieldName,
        `${fieldName} must be at least ${min} characters long`
      );
    }
    if (max && value.length > max) {
      return this.validationError(
        fieldName,
        `${fieldName} must be no more than ${max} characters long`
      );
    }
    return null;
  }

  protected validateUUID(value: string, fieldName: string): ServiceResponse | null {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(value)) {
      return this.validationError(fieldName, `${fieldName} must be a valid UUID`);
    }
    return null;
  }

  // ============================================================================
  // Error Handling Helpers
  // ============================================================================

  protected async handleRepositoryOperation<T>(
    operation: () => Promise<T>,
    errorMessage: string = 'Operation failed'
  ): Promise<ServiceResponse<T>> {
    try {
      const result = await operation();
      return this.success(result);
    } catch (error: any) {
      console.error(`Repository operation failed: ${errorMessage}`, error);
      
      // Map common database errors to service errors
      if (error.message?.includes('not found')) {
        return this.notFoundError('Resource');
      }
      
      if (error.message?.includes('unique constraint') || 
          error.message?.includes('duplicate key')) {
        return this.conflictError('Resource already exists');
      }
      
      if (error.message?.includes('foreign key constraint')) {
        return this.validationError('reference', 'Referenced resource does not exist');
      }

      return this.internalError(errorMessage, error.message);
    }
  }

  // ============================================================================
  // Utility Methods
  // ============================================================================

  protected generateId(): string {
    // Simple UUID v4 generator (in production, use a proper UUID library)
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c == 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  protected sanitizeString(value: string): string {
    return value.trim().replace(/\s+/g, ' ');
  }

  protected formatDate(date: Date | string): string {
    const d = typeof date === 'string' ? new Date(date) : date;
    return d.toISOString();
  }

  protected isValidDate(date: string): boolean {
    const d = new Date(date);
    return d instanceof Date && !isNaN(d.getTime());
  }

  // ============================================================================
  // Logging Helpers
  // ============================================================================

  protected log(level: 'info' | 'warn' | 'error', message: string, data?: any): void {
    const timestamp = new Date().toISOString();
    const serviceName = this.constructor.name;
    
    const logMessage = `[${timestamp}] [${level.toUpperCase()}] [${serviceName}] ${message}`;
    
    switch (level) {
      case 'info':
        console.log(logMessage, data || '');
        break;
      case 'warn':
        console.warn(logMessage, data || '');
        break;
      case 'error':
        console.error(logMessage, data || '');
        break;
    }
  }

  protected logInfo(message: string, data?: any): void {
    this.log('info', message, data);
  }

  protected logWarn(message: string, data?: any): void {
    this.log('warn', message, data);
  }

  protected logError(message: string, data?: any): void {
    this.log('error', message, data);
  }
}
