# 🧠 PLANNING.md — Project Constitution

## Table of Contents
1. [Vision](#vision)
2. [Core Principles](#core-principles)
3. [AI Context & Automation](#ai-context--automation)
4. [Feature Epics](#feature-epics)
5. [Modular Structure](#modular-structure)
6. [How to Extend](#how-to-extend)
7. [References](#references)

---

## Vision
A robust, open-source scaffold for launching online course and certification platforms, designed for rapid rebranding, multi-tenancy, and AI coding agent collaboration.

---

## Core Principles
- **Context Engineering:** All docs and code are structured for AI agent and human consumption.
- **Reusability:** Easy to fork, rebrand, and deploy for any client or vertical.
- **Automation:** CI/CD, dependency updates, and backups are automated.
- **Security:** RLS, audit logging, and best practices by default.
- **Design System:** Atomic, accessible, and brandable components.
- **Documentation:** Every feature, workflow, and policy is documented for both humans and AI agents.

---

## AI Context & Automation
- All docs and code are structured for both human and AI agent (bot) consumption.
- **Jules**: Automates code changes, refactors, and documentation from issues labeled `jules`. See [AI_CONTEXT.md](./AI_CONTEXT.md).
- **coderabbitai**: Provides code suggestions, refactoring, and documentation in PRs and issues.
- **Renovate/Dependabot**: Keep all dependencies up-to-date via automated PRs, triggering CI/CD and auto-merge if safe.
- **Metadata & Embeddings**: Automated scripts generate project metadata and vector embeddings for LLM/AI agent context, updated on every push to main.
- **Vercel/Google Cloud Build**: Automated deploys for preview and production, with status notifications in PRs.
- **All bots are configured in the bypass list for seamless automation.**
- **All workflows and bot actions are documented in [AI_CONTEXT.md](./AI_CONTEXT.md).**

---

## Feature Epics
- See [TAKS.md](./TAKS.md) for granular tasks and progress.
- See [RULES.md](./RULES.md) for coding rules and workflow.
- See [ARCHITECTURE_SUSTAINABILITY.md](./ARCHITECTURE_SUSTAINABILITY.md) for best practices.

---

## Modular Structure
- `src/app/` — Next.js App Router, pages, layouts
- `src/components/` — UI and domain components
- `src/lib/` — Utilities, Supabase client, helpers
- `public/` — Static assets, logos, images
- `docs/` — Planning, rules, design system, security, backup/migration
- `.github/workflows/` — CI/CD automation

---

## How to Extend
- Add new features as atomic modules/components
- Document all new features in `docs/`
- Update this file with new epics and context as the project evolves
- For rules, see [RULES.md](./RULES.md)
- For design, see [DESIGN_SYSTEM.md](./DESIGN_SYSTEM.md)
- For security, see [SECURITY.md](./SECURITY.md)
- For backup/migration, see [BACKUP_MIGRATION.md](./BACKUP_MIGRATION.md)

---

## References
- [Context Engineering Template](https://github.com/iberi22/context-engineering-template)
- [README.md](../README.md)
- [RULES.md](./RULES.md)
- [TAKS.md](./TAKS.md)
- [DESIGN_SYSTEM.md](./DESIGN_SYSTEM.md)
- [BACKUP_MIGRATION.md](./BACKUP_MIGRATION.md)
- [SECURITY.md](./SECURITY.md)
- [AI_CONTEXT.md](./AI_CONTEXT.md)
- [ARCHITECTURE_SUSTAINABILITY.md](./ARCHITECTURE_SUSTAINABILITY.md)

---

## Modernization Roadmap (Phased Plan)

> See the [README](../README.md#-plan-de-modernización-y-mejora-continua-faseshitos) for the full modernization plan and phase details.

- **Branding & Documentation:** [DESIGN_SYSTEM.md](./DESIGN_SYSTEM.md), [INDEX.md](./INDEX.md)
- **Architecture & Adapters:** [ARCHITECTURE_SUSTAINABILITY.md](./ARCHITECTURE_SUSTAINABILITY.md)
- **CI/CD & Backups:** [BACKUP_MIGRATION.md](./BACKUP_MIGRATION.md)
- **AI Context & Bots:** [AI_CONTEXT.md](./AI_CONTEXT.md)
- **Onboarding & Sustainability:** [INDEX.md](./INDEX.md)

Each phase is cross-referenced in the relevant manual. All docs follow the [context-engineering-template](https://github.com/iberi22/context-engineering-template) style for modularity and AI-readiness.

---

> This file is the "constitution" for the project. All other docs are referenced here for modularity and clarity.