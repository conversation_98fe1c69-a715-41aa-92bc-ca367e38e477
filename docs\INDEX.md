# 📚 Documentation Index

Welcome to the documentation hub for this scaffold. This index provides a clear, modular map of all project manuals and guides, following the [Context Engineering Template](https://github.com/iberi22/context-engineering-template) style.

For a high-level overview, see the [README](../README.md).

---

## Roadmap & Modernization Plan

See the [Modernization Plan in README](../README.md#-plan-de-modernización-y-mejora-continua-faseshitos) for a detailed, phased approach to project improvement, including:
- Branding & Documentation Unification
- Architecture & Adapter Refactor
- CI/CD & Automation
- AI Context & Bot Orchestration
- Onboarding & Sustainability

Each phase is cross-referenced in the relevant manual below.

---

## Table of Contents

| Document                                 | Purpose/Contents                                                      |
|------------------------------------------|----------------------------------------------------------------------|
| [PLANNING.md](./PLANNING.md)             | Project constitution: vision, principles, context, epics, structure   |
| [RULES.md](./RULES.md)                   | Quick rulebook: stack, workflow, coding principles, usage guide       |
| [TAKS.md](./TAKS.md)                     | Task board: granular tasks, milestones, progress tracking             |
| [AI_CONTEXT.md](./AI_CONTEXT.md)         | AI/LLM context: bot orchestration, metadata, embeddings, automation   |
| [SECURITY.md](./SECURITY.md)             | Security manual: RLS, secrets, audit logging, best practices          |
| [BACKUP_MIGRATION.md](./BACKUP_MIGRATION.md) | Backup & migration: guides, automation, best practices            |
| [DESIGN_SYSTEM.md](./DESIGN_SYSTEM.md)   | Design system: branding, components, accessibility, customization     |
| [ARCHITECTURE_SUSTAINABILITY.md](./ARCHITECTURE_SUSTAINABILITY.md) | Advanced architecture & sustainability best practices |

---

## How to Use
- Start with **PLANNING.md** for vision and structure.
- Use **RULES.md** for quick reference on stack, workflow, and coding standards.
- Track progress and tasks in **TAKS.md**.
- For AI/bot integration, see **AI_CONTEXT.md**.
- For security, backup, design, and architecture, see the respective manuals above.
- For the full modernization plan, see the [README](../README.md#-plan-de-modernización-y-mejora-continua-faseshitos).

> Keep this index up-to-date as you add or reorganize documentation.