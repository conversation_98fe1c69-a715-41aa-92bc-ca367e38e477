# 📏 RULES.md — Project Rulebook

## Table of Contents
1. [Purpose](#purpose)
2. [Quick Project Summary](#quick-project-summary)
3. [Stack & Workflow](#stack--workflow)
4. [Coding Principles](#coding-principles)
5. [How to Use This Template](#how-to-use-this-template)
6. [References](#references)

---

## Purpose
This document defines the essential context and rules for developing and maintaining the QR Course Scaffold. It is the main quick-reference for both human developers and AI coding agents. For details, always refer to the referenced manuals.

---

## Quick Project Summary
- **Goal:** Rapidly deliver a robust, maintainable, and AI/LLM-friendly online course/certification platform.
- **Backend:** Supabase (Auth, PostgreSQL, Storage, Edge Functions)
- **Frontend:** Next.js (App Router, TypeScript)
- **UI/UX:** Tailwind CSS, Shadcn/ui, custom atomic components
- **Deployment:** Vercel (Frontend), Supabase (Backend)
- **AI Context:** Automated metadata, embeddings, and bot orchestration ([AI_CONTEXT.md](./AI_CONTEXT.md))

---

## Stack & Workflow
- **Framework:** Next.js (App Router, TypeScript)
- **Backend/DB:** Supabase Platform
- **DB Client:** @supabase/supabase-js (preferred for MVP)
- **UI/Styles:** Tailwind CSS + Shadcn/ui
- **QR Generation:** `qrcode` (Node.js, API Routes/Edge Functions)
- **CI/CD:** GitHub Actions, Vercel
- **Testing:** Playwright (E2E), Vitest (unit)
- **Backup/Migration:** See [BACKUP_MIGRATION.md](./BACKUP_MIGRATION.md)
- **Security:** RLS, secrets, audit logging ([SECURITY.md](./SECURITY.md))
- **Design System:** See [DESIGN_SYSTEM.md](./DESIGN_SYSTEM.md)
- **Architecture:** Modular, context-driven ([PLANNING.md](./PLANNING.md), [ARCHITECTURE_SUSTAINABILITY.md](./ARCHITECTURE_SUSTAINABILITY.md))

---

## Coding Principles
- **TypeScript:** Use clear, well-typed code.
- **Style:** Follow Next.js, React, and Tailwind conventions. Comment complex logic.
- **Error Handling:** Basic error handling in frontend/backend.
- **Security:** Prioritize RLS, validate all backend inputs, never expose secrets.
- **MVP Focus:** Stick to defined features and epics. Avoid unnecessary complexity.
- **Documentation:** Every module/component should have a docstring/context comment.
- **Automation:** Use and maintain scripts/workflows for metadata, audit, backup, and CI/CD.

---

## How to Use This Template
1. **Start with [PLANNING.md](./PLANNING.md):** Understand the vision, principles, and modular structure.
2. **Follow this RULES.md:** For quick reference on stack, workflow, and coding standards.
3. **Use [TAKS.md](./TAKS.md):** For granular task tracking and progress.
4. **Consult [AI_CONTEXT.md](./AI_CONTEXT.md):** For bot/AI agent integration and context automation.
5. **Refer to technical manuals:**
   - [SECURITY.md](./SECURITY.md) for security best practices
   - [BACKUP_MIGRATION.md](./BACKUP_MIGRATION.md) for backup/migration
   - [DESIGN_SYSTEM.md](./DESIGN_SYSTEM.md) for design/branding
   - [ARCHITECTURE_SUSTAINABILITY.md](./ARCHITECTURE_SUSTAINABILITY.md) for advanced architecture
6. **Update docs as you extend the project.**

---

## References
- [PLANNING.md](./PLANNING.md)
- [TAKS.md](./TAKS.md)
- [AI_CONTEXT.md](./AI_CONTEXT.md)
- [SECURITY.md](./SECURITY.md)
- [BACKUP_MIGRATION.md](./BACKUP_MIGRATION.md)
- [DESIGN_SYSTEM.md](./DESIGN_SYSTEM.md)
- [ARCHITECTURE_SUSTAINABILITY.md](./ARCHITECTURE_SUSTAINABILITY.md)
- [Context Engineering Template](https://github.com/iberi22/context-engineering-template)

---

## Modernization Plan Reference

- See the [README](../README.md#-plan-de-modernización-y-mejora-continua-faseshitos) for the full phased roadmap.
- Each rule and workflow is cross-referenced in the relevant manual below.
- All docs follow the [context-engineering-template](https://github.com/iberi22/context-engineering-template) style for modularity and AI-readiness.

---

> This file is your main quick-reference for rules and context. For details, always check the referenced manuals.